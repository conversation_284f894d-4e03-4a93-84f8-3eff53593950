'use client';

import React, { useState, useEffect } from 'react';
import { Heart, Zap, Crown, Gem, Globe, Clock, Plus, ArrowLeft, <PERSON>rkles, Gift } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useTranslation } from '@/app/i18n/client';
import { useAuthContext } from '@/components/AuthProvider';
import MindFuelRechargeModal from '@/components/mind-fuel/MindFuelRechargeModal';
import MembershipUpgradePromo from '@/components/mind-fuel/MembershipUpgradePromo';
import QuickUseItemModal from '@/components/mind-fuel/QuickUseItemModal';

interface MindFuelClientPageProps {
  lang: string;
}

const MindFuelClientPage: React.FC<MindFuelClientPageProps> = ({ lang }) => {
  const { t } = useTranslation(lang, 'translation');
  const { user } = useAuthContext();
  const router = useRouter();
  const [currentTime, setCurrentTime] = useState(Date.now());
  const [showRechargeModal, setShowRechargeModal] = useState(false);
  const [showUseItemModal, setShowUseItemModal] = useState(false);

  // 更新时间
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(Date.now());
    }, 1000);
    return () => clearInterval(interval);
  }, []);

  // 根据会员等级获取思维燃料配置
  const getMindFuelConfig = (membershipTier: string = 'standard') => {
    switch (membershipTier) {
      case 'pass':
        return {
          maxMindFuel: 20,
          recoverySpeed: 3,
          recoveryInterval: 30 * 60 * 1000, // 30分钟
          tierName: t('mindFuel.membership.tiers.pass'),
          tierColor: 'from-blue-400 to-blue-600',
          icon: Crown,
        };
      case 'diamond':
        return {
          maxMindFuel: 50,
          recoverySpeed: 10,
          recoveryInterval: 12 * 60 * 1000, // 12分钟
          tierName: t('mindFuel.membership.tiers.diamond'),
          tierColor: 'from-cyan-400 to-cyan-600',
          icon: Gem,
        };
      case 'metaverse':
        return {
          maxMindFuel: Infinity,
          recoverySpeed: Infinity,
          recoveryInterval: 0,
          tierName: t('mindFuel.membership.tiers.metaverse'),
          tierColor: 'from-purple-400 to-purple-600',
          icon: Globe,
        };
      default:
        return {
          maxMindFuel: 10,
          recoverySpeed: 1,
          recoveryInterval: 60 * 60 * 1000, // 60分钟
          tierName: t('mindFuel.membership.tiers.standard'),
          tierColor: 'from-gray-400 to-gray-600',
          icon: Heart,
        };
    }
  };

  const config = getMindFuelConfig(user?.membership_tier);
  const currentMindFuel = user?.stamina_current || 8;
  const maxMindFuel = config.maxMindFuel === Infinity ? Infinity : config.maxMindFuel;
  const recoveryTime = user?.stamina_recovery_time || Date.now() + 45 * 60 * 1000;
  const IconComponent = config.icon;

  // 模拟用户数据
  const userData = {
    level: 12,
    exp: 2450,
    nextLevelExp: 3000,
    currency: {
      alphane: user?.alphane_dust_balance || 1500,
      endora: user?.endora_crystal_balance || 250
    },
    isFirstPurchase: true
  };

  // 计算回复时间
  const getRecoveryInfo = () => {
    if (config.maxMindFuel === Infinity) {
      return { text: t('mindFuel.unlimitedMindFuel'), progress: 100 };
    }

    if (currentMindFuel >= config.maxMindFuel) {
      return { text: t('mindFuel.mindFuelFull'), progress: 100 };
    }

    const timeLeft = recoveryTime - currentTime;
    if (timeLeft <= 0) {
      return { text: t('mindFuel.aboutToRecover'), progress: 100 };
    }
    
    const totalInterval = config.recoveryInterval;
    const progress = ((totalInterval - timeLeft) / totalInterval) * 100;
    
    const minutes = Math.floor(timeLeft / (60 * 1000));
    const seconds = Math.floor((timeLeft % (60 * 1000)) / 1000);
    const hours = Math.floor(minutes / 60);
    
    let timeText = '';
    if (hours > 0) {
      timeText = `${hours}:${String(minutes % 60).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
    } else {
      timeText = `${minutes}:${String(seconds).padStart(2, '0')}`;
    }
    
    return { text: timeText, progress };
  };

  const recoveryInfo = getRecoveryInfo();

  const handleRecharge = () => {
    setShowRechargeModal(true);
  };

  const handleRechargeConfirm = (option: any) => {
    // 这里实现实际的充值逻辑
    console.log('Recharging with option:', option);
    // 模拟充值成功
    const currencyName = option.currency === 'alphane' ? '曦光微尘' : '心悦晶石';
    alert(t('mindFuel.recharge.success', { cost: option.cost, currency: currencyName }));
  };

  const handleUpgradeMembership = (tier: 'standard' | 'pass' | 'diamond' | 'metaverse') => {
    console.log('Upgrading to:', tier);
    // 这里应该调用API进行会员升级
    const tierName = t(`mindFuel.membership.tiers.${tier}`);
    alert(`升级到 ${tierName} 成功！`);
  };

  const handleUseItem = (item: any, quantity: number) => {
    console.log('Using item:', item, 'quantity:', quantity);
    // 这里应该调用API使用道具
    alert(t('mindFuel.items.success', { quantity, itemName: item.name }));
  };

  const handleGoToStore = () => {
    router.push(`/${lang}/store`);
  };

  return (
    <div className="min-h-screen p-4 max-w-2xl mx-auto">
      {/* Header */}
      <div className="flex items-center gap-4 mb-8">
        <button
          onClick={() => router.back()}
          className="p-3 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-xl shadow-lg hover:scale-105 transition-all duration-200"
        >
          <ArrowLeft className="w-5 h-5 text-gray-600 dark:text-gray-300" />
        </button>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">{t('mindFuel.title')}</h1>
      </div>

      <div className="space-y-8">
          {/* 主要思维燃料显示卡片 - 优化版 */}
          <div className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-3xl p-8 shadow-xl border border-gray-200/50 dark:border-gray-700/50">
            <div className="text-center mb-8">
              {/* 简化的心心图标 */}
              <div className="relative w-24 h-24 mx-auto mb-6">
                <div className="w-full h-full bg-gradient-to-br from-red-400 to-pink-500 rounded-full flex items-center justify-center shadow-lg">
                  <Heart className="w-12 h-12 text-white fill-current" />
                </div>
                {/* 简化的进度环 */}
                <div className="absolute -inset-1">
                  <svg className="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
                    <circle
                      cx="50"
                      cy="50"
                      r="47"
                      stroke="currentColor"
                      strokeWidth="3"
                      fill="none"
                      className="text-gray-200 dark:text-gray-700"
                    />
                    <circle
                      cx="50"
                      cy="50"
                      r="47"
                      stroke="currentColor"
                      strokeWidth="3"
                      fill="none"
                      strokeDasharray={`${2 * Math.PI * 47}`}
                      strokeDashoffset={`${2 * Math.PI * 47 * (1 - (typeof maxMindFuel === 'number' ? currentMindFuel / maxMindFuel : 1))}`}
                      className="text-red-500 transition-all duration-500"
                      strokeLinecap="round"
                    />
                  </svg>
                </div>
              </div>

              {/* 思维燃料数值 */}
              <h2 className="text-4xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                {currentMindFuel} / {typeof maxMindFuel === 'number' ? maxMindFuel : '∞'}
              </h2>
              <p className="text-gray-600 dark:text-gray-400 text-base mb-4">{t('mindFuel.currentMindFuel')}</p>

              {/* 会员等级显示 */}
              <div className="inline-flex items-center gap-3 bg-gray-50 dark:bg-gray-700/50 rounded-full px-4 py-2">
                <div className={`w-6 h-6 bg-gradient-to-br ${config.tierColor} rounded-lg flex items-center justify-center`}>
                  <IconComponent className="w-3 h-3 text-white" />
                </div>
                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  {config.tierName}
                </span>
              </div>
            </div>

            {/* 回复进度条 */}
            {config.maxMindFuel !== Infinity && currentMindFuel < config.maxMindFuel && (
              <div className="mb-8">
                <div className="flex items-center justify-between mb-3">
                  <span className="text-sm font-medium text-gray-600 dark:text-gray-400">{t('mindFuel.nextRecovery')}</span>
                  <span className="text-sm font-mono font-medium text-gray-900 dark:text-gray-100">
                    {recoveryInfo.text}
                  </span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 overflow-hidden">
                  <div
                    className="h-full bg-gradient-to-r from-red-400 to-pink-500 rounded-full transition-all duration-500"
                    style={{ width: `${recoveryInfo.progress}%` }}
                  />
                </div>
              </div>
            )}

            {/* 快速操作按钮 */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <button
                onClick={handleRecharge}
                className="bg-gradient-to-r from-red-500 to-pink-500 text-white py-4 px-6 rounded-2xl font-semibold hover:scale-105 transition-all duration-200 shadow-lg flex items-center justify-center gap-2"
              >
                <Plus className="w-5 h-5" />
                <span>{t('mindFuel.quickRecharge')}</span>
              </button>
              <button
                onClick={() => setShowUseItemModal(true)}
                className="bg-gradient-to-r from-blue-500 to-purple-500 text-white py-4 px-6 rounded-2xl font-semibold hover:scale-105 transition-all duration-200 shadow-lg flex items-center justify-center gap-2"
              >
                <Sparkles className="w-5 h-5" />
                <span>{t('mindFuel.useItem')}</span>
              </button>
              <button
                onClick={handleGoToStore}
                className="bg-gradient-to-r from-green-500 to-emerald-500 text-white py-4 px-6 rounded-2xl font-semibold hover:scale-105 transition-all duration-200 shadow-lg flex items-center justify-center gap-2"
              >
                <Gift className="w-5 h-5" />
                <span>{t('mindFuel.goToStore')}</span>
              </button>
            </div>
          </div>

        {/* 会员升级推销 */}
        <MembershipUpgradePromo
          currentTier={user?.membership_tier || 'standard'}
          currentMindFuel={currentMindFuel}
          maxMindFuel={typeof maxMindFuel === 'number' ? maxMindFuel : 10}
          lang={lang}
          onUpgrade={handleUpgradeMembership}
        />
      </div>

      {/* 充值模态框 */}
      <MindFuelRechargeModal
        isOpen={showRechargeModal}
        onClose={() => setShowRechargeModal(false)}
        currentMindFuel={currentMindFuel}
        maxMindFuel={typeof maxMindFuel === 'number' ? maxMindFuel : Infinity}
        lang={lang}
        onRecharge={handleRechargeConfirm}
      />

      {/* 道具使用模态框 */}
      <QuickUseItemModal
        isOpen={showUseItemModal}
        onClose={() => setShowUseItemModal(false)}
        currentMindFuel={currentMindFuel}
        maxMindFuel={typeof maxMindFuel === 'number' ? maxMindFuel : 10}
        lang={lang}
        onUseItem={handleUseItem}
        onGoToStore={handleGoToStore}
      />
    </div>
  );
};

export default MindFuelClientPage;
