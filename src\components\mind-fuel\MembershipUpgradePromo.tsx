'use client';

import React from 'react';
import { Crown, Zap, Globe, Gem, Heart, Clock, Star, TrendingUp, Gift, ChevronRight } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';
import { useRouter } from 'next/navigation';

interface MembershipTier {
  level: 'standard' | 'pass' | 'diamond' | 'metaverse';
  name: string;
  price: number;
  originalPrice?: number;
  mindFuelMax: number | string;
  recoveryTime: string;
  features: string[];
  exclusiveFeatures: string[];
  color: string;
  gradient: string;
  icon: React.ComponentType<any>;
  popular?: boolean;
  recommended?: boolean;
}

interface MembershipUpgradePromoProps {
  currentTier: 'standard' | 'pass' | 'diamond' | 'metaverse';
  currentMindFuel: number;
  maxMindFuel: number;
  lang: string;
  onUpgrade: (tier: 'standard' | 'pass' | 'diamond' | 'metaverse') => void;
}

const MembershipUpgradePromo: React.FC<MembershipUpgradePromoProps> = ({
  currentTier,
  currentMindFuel,
  maxMindFuel,
  lang,
  onUpgrade
}) => {
  const { t } = useTranslation(lang, 'translation');
  const router = useRouter();

  const membershipTiers: MembershipTier[] = [
    {
      level: 'standard',
      name: t('mindFuel.membership.tiers.standard'),
      price: 0,
      mindFuelMax: 10,
      recoveryTime: t('mindFuel.membership.recoveryTimes.perHour'),
      features: [
        t('mindFuel.membership.features.basicChat'),
        t('mindFuel.membership.features.dailyAI'),
        t('mindFuel.membership.features.communityAccess')
      ],
      exclusiveFeatures: [],
      color: 'gray',
      gradient: 'from-gray-400 to-gray-600',
      icon: Heart
    },
    {
      level: 'pass',
      name: t('mindFuel.membership.tiers.pass'),
      price: 9.99,
      originalPrice: 12.99,
      mindFuelMax: 20,
      recoveryTime: t('mindFuel.membership.recoveryTimes.per30Min'),
      features: [
        '高级AI模型对话',
        '图片互动功能',
        '记忆胶囊容量500个'
      ],
      exclusiveFeatures: [
        t('mindFuel.membership.features.exclusiveAvatar'),
        t('mindFuel.membership.features.memberBadge')
      ],
      color: 'blue',
      gradient: 'from-blue-400 to-blue-600',
      icon: Crown,
      popular: true
    },
    {
      level: 'diamond',
      name: t('mindFuel.membership.tiers.diamond'),
      price: 29.99,
      originalPrice: 39.99,
      mindFuelMax: 50,
      recoveryTime: t('mindFuel.membership.recoveryTimes.per12Min'),
      features: [
        '包含所有Pass会员权益',
        '图片+语音互动功能',
        '记忆胶囊容量2000个'
      ],
      exclusiveFeatures: [
        t('mindFuel.membership.features.diamondBadge'),
        t('mindFuel.membership.features.exclusiveEffects'),
        t('mindFuel.membership.features.advancedAnalysis')
      ],
      color: 'purple',
      gradient: 'from-purple-400 to-pink-400',
      icon: Gem,
      recommended: true
    },
    {
      level: 'metaverse',
      name: t('mindFuel.membership.tiers.metaverse'),
      price: 49.99,
      originalPrice: 69.99,
      mindFuelMax: '∞',
      recoveryTime: t('mindFuel.membership.recoveryTimes.noWait'),
      features: [
        t('mindFuel.membership.features.metaverseExperience'),
        t('mindFuel.membership.features.allFeatures'),
        t('mindFuel.membership.features.personalAdvisor'),
        t('mindFuel.membership.features.limitedContent'),
        t('mindFuel.membership.features.highestPriority'),
        t('mindFuel.membership.features.unlimitedMindFuel')
      ],
      exclusiveFeatures: [
        t('mindFuel.membership.features.metaverseBadge'),
        t('mindFuel.membership.features.exclusiveAnimations'),
        t('mindFuel.membership.features.personalConsultant'),
        t('mindFuel.membership.features.vipAccess')
      ],
      color: 'indigo',
      gradient: 'from-indigo-400 via-purple-400 to-pink-400',
      icon: Globe
    }
  ];

  const getCurrentTierIndex = () => {
    return membershipTiers.findIndex(tier => tier.level === currentTier);
  };

  const getUpgradableTiers = () => {
    const currentIndex = getCurrentTierIndex();
    return membershipTiers.filter((_, index) => index > currentIndex);
  };

  const getMindFuelComparison = (tier: MembershipTier) => {
    const current = maxMindFuel;
    const upgraded = typeof tier.mindFuelMax === 'string' ? '∞' : tier.mindFuelMax;
    const increase = typeof tier.mindFuelMax === 'string' ? '∞' : tier.mindFuelMax - current;
    return { current, upgraded, increase };
  };

  const handleGoToMembership = () => {
    router.push(`/${lang}/store?tab=memberships`);
  };



  const upgradableTiers = getUpgradableTiers();

  if (currentTier === 'metaverse') {
    return (
      <div className="bg-gradient-to-r from-indigo-500/10 via-purple-500/10 to-pink-500/10 border border-indigo-500/20 rounded-2xl p-6">
        <div className="text-center">
          <Globe className="w-12 h-12 text-indigo-400 mx-auto mb-4" />
          <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-2">
            {t('mindFuel.membership.comparison.maxTierReached')}
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            {t('mindFuel.membership.comparison.maxTierDescription')}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 主推销卡片 */}
      <div className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-3xl p-8 shadow-xl border border-orange-200/50 dark:border-orange-700/50 relative overflow-hidden">
        {/* 简化的装饰元素 */}
        <div className="absolute top-4 right-4 text-3xl opacity-30">
          ⚡
        </div>

        <div className="relative">
          <div className="mb-6">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-3">
              🚀 升级会员，思维燃料翻倍！
            </h3>
            <p className="text-gray-600 dark:text-gray-400 text-base">
              解锁更大容量和更快恢复速度，让创意永不枯竭
            </p>
          </div>

          {/* 对比展示 */}
          <div className="grid grid-cols-2 gap-4 mb-8">
            <div className="bg-gray-50 dark:bg-gray-700/50 rounded-2xl p-5 text-center">
              <div className="text-gray-500 dark:text-gray-400 text-sm font-medium mb-2">{t('mindFuel.membership.currentTier')}</div>
              <div className="text-3xl font-bold text-gray-700 dark:text-gray-300 mb-1">{maxMindFuel}</div>
              <div className="text-xs text-gray-500">{t('mindFuel.membership.mindFuelLimit')}</div>
            </div>
            <div className="bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border-2 border-blue-200 dark:border-blue-700 rounded-2xl p-5 text-center">
              <div className="text-blue-600 dark:text-blue-400 text-sm font-medium mb-2">升级后容量</div>
              <div className="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-1">
                {upgradableTiers[0] ? (typeof upgradableTiers[0].mindFuelMax === 'string' ? '∞' : upgradableTiers[0].mindFuelMax) : '20'}
              </div>
              <div className="text-xs text-blue-500">思维燃料上限</div>
            </div>
          </div>

          {/* 立即升级按钮 */}
          <button
            onClick={() => upgradableTiers[0] && onUpgrade(upgradableTiers[0].level)}
            className="w-full bg-gradient-to-r from-orange-500 to-red-500 text-white py-4 px-6 rounded-2xl font-semibold text-lg hover:scale-105 transition-all duration-200 shadow-lg flex items-center justify-center gap-3"
          >
            <Crown className="w-5 h-5" />
            立即升级会员
            <ChevronRight className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* 会员等级选择 */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
        {upgradableTiers.slice(0, 2).map((tier) => {
          const IconComponent = tier.icon;
          const comparison = getMindFuelComparison(tier);
          const discount = tier.originalPrice ? Math.round((1 - tier.price / tier.originalPrice) * 100) : 0;

          return (
            <div
              key={tier.level}
              className={`bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-3xl p-6 border border-gray-200/50 dark:border-gray-700/50 shadow-xl hover:scale-105 transition-all duration-200 relative ${
                tier.popular ? 'ring-2 ring-blue-400/50' : ''
              } ${tier.recommended ? 'ring-2 ring-purple-400/50' : ''}`}
            >
              {/* 标签 */}
              <div className="absolute -top-3 -right-3 flex gap-2">
                {tier.popular && (
                  <span className="bg-blue-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg">
                    {t('mindFuel.membership.comparison.hot')}
                  </span>
                )}
                {tier.recommended && (
                  <span className="bg-purple-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg">
                    {t('mindFuel.membership.comparison.recommended')}
                  </span>
                )}
                {discount > 0 && (
                  <span className="bg-red-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg">
                    {t('mindFuel.membership.comparison.discount', { percent: discount })}
                  </span>
                )}
              </div>

              {/* 会员图标 */}
              <div className={`w-14 h-14 bg-gradient-to-br ${tier.gradient} rounded-2xl flex items-center justify-center mb-5 shadow-lg`}>
                <IconComponent className="w-7 h-7 text-white" />
              </div>

              {/* 会员信息 */}
              <h4 className="font-bold text-xl text-gray-900 dark:text-gray-100 mb-3">
                {tier.name}
              </h4>

              {/* 价格 */}
              <div className="mb-4">
                {tier.originalPrice && (
                  <div className="text-sm text-gray-500 line-through mb-1">
                    {t('mindFuel.membership.comparison.originalPrice', { price: tier.originalPrice })}
                  </div>
                )}
                <div className="text-3xl font-bold text-gray-900 dark:text-gray-100">
                  ${tier.price}<span className="text-base font-normal text-gray-600 dark:text-gray-400">{t('mindFuel.membership.comparison.month')}</span>
                </div>
              </div>

              {/* 思维燃料对比 */}
              <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-2xl p-4 mb-4">
                <div className="text-sm text-green-700 dark:text-green-400 space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="font-medium">{t('mindFuel.membership.comparison.mindFuelLimit')}</span>
                    <span className="font-bold">
                      {comparison.current} → {comparison.upgraded}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="font-medium">{t('mindFuel.membership.comparison.recoverySpeed')}</span>
                    <span className="font-bold">{tier.recoveryTime}</span>
                  </div>
                </div>
              </div>

              {/* 核心特权 */}
              <div className="space-y-2 mb-5">
                {tier.features.slice(0, 3).map((feature, index) => (
                  <div key={index} className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                    <Star className="w-4 h-4 text-yellow-500 flex-shrink-0" />
                    <span>{feature}</span>
                  </div>
                ))}
              </div>

              {/* 升级按钮 */}
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onUpgrade(tier.level);
                }}
                className={`w-full py-4 px-6 bg-gradient-to-r ${tier.gradient} text-white rounded-2xl font-semibold hover:scale-105 transition-all duration-200 shadow-lg mb-3`}
              >
                {t('mindFuel.membership.comparison.upgradeToTier', { tierName: tier.name })}
              </button>

              {/* 查看完整权益链接 */}
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleGoToMembership();
                }}
                className="w-full text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors flex items-center justify-center gap-1"
              >
                查看完整权益详情
                <ChevronRight className="w-3 h-3" />
              </button>
            </div>
          );
        })}
      </div>

      {/* 查看所有会员等级 */}
      {upgradableTiers.length > 2 && (
        <button
          onClick={handleGoToMembership}
          className="w-full py-3 px-4 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-xl font-medium hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors flex items-center justify-center gap-2"
        >
          {t('mindFuel.membership.comparison.viewAllTiers')}
          <ChevronRight className="w-4 h-4" />
        </button>
      )}
    </div>
  );
};

export default MembershipUpgradePromo;
